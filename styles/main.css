:root {
  --primary-color: #4285f4;
  --secondary-color: #0f9d58;
  --danger-color: #ea4335;
  --text-color: #333;
  --text-secondary: #666;
  --bg-color: #fff;
  --card-bg-color: #fff;
  --hover-color: #f5f5f5;
  --border-color: #ddd;
  --shadow-color: rgba(0, 0, 0, 0.1);
  --overlay-color: rgba(255, 255, 255, 0.7);
  --background-color: #ffffff;
  --card-background: rgba(255, 255, 255, 0.9);
  --modal-background: rgba(255, 255, 255, 0.98);
  --border-radius: 12px;
  --transition-speed: 0.3s;
}

/* 主题样式 */
body.theme-default {
  --bg-color: #fff;
  --card-bg-color: #fff;
  --text-color: #333;
  --text-secondary: #666;
  --border-color: #ddd;
  --hover-color: #f5f5f5;
  --overlay-color: rgba(255, 255, 255, 0.7);
}

body.theme-blue {
  --bg-color: #e8f0fe;
  --card-bg-color: #fff;
  --text-color: #333;
  --text-secondary: #4285f4;
  --border-color: #c6dafc;
  --hover-color: #d2e3fc;
  --overlay-color: rgba(232, 240, 254, 0.7);
}

body.theme-green {
  --bg-color: #e6f4ea;
  --card-bg-color: #fff;
  --text-color: #333;
  --text-secondary: #0f9d58;
  --border-color: #ceead6;
  --hover-color: #d8eee1;
  --overlay-color: rgba(230, 244, 234, 0.7);
}

body.theme-purple {
  --bg-color: #f3e8fd;
  --card-bg-color: #fff;
  --text-color: #333;
  --text-secondary: #673ab7;
  --border-color: #e5d4fa;
  --hover-color: #ead6fd;
  --overlay-color: rgba(243, 232, 253, 0.7);
}

body.theme-pink {
  --bg-color: #fce4ec;
  --card-bg-color: #fff;
  --text-color: #333;
  --text-secondary: #e91e63;
  --border-color: #f8bbd0;
  --hover-color: #f8d0e0;
  --overlay-color: rgba(252, 228, 236, 0.7);
}

body.theme-dark {
  --bg-color: #292a2d;
  --card-bg-color: #35363a;
  --text-color: #e8eaed;
  --text-secondary: #9aa0a6;
  --border-color: #5f6368;
  --hover-color: #3c4043;
  --overlay-color: rgba(41, 42, 45, 0.7);
  --shadow-color: rgba(0, 0, 0, 0.3);
}

/* 深色主题下的输入框优化 */
body.theme-dark .form-group input[type="text"],
body.theme-dark .form-group input[type="url"],
body.theme-dark .form-group input[type="password"] {
  background-color: #202124;
  border-color: #5f6368;
  color: #ffffff;
  font-weight: 500;
}

body.theme-dark .form-group input[type="text"]:focus,
body.theme-dark .form-group input[type="url"]:focus,
body.theme-dark .form-group input[type="password"]:focus {
  border-color: #8ab4f8;
  box-shadow: 0 0 0 3px rgba(138, 180, 248, 0.2);
  background-color: #1a1a1a;
}

body.theme-dark .form-group input[type="text"]:hover,
body.theme-dark .form-group input[type="url"]:hover,
body.theme-dark .form-group input[type="password"]:hover {
  border-color: #80868b;
  background-color: #1a1a1a;
}

body.theme-dark .form-group input[type="text"]::placeholder,
body.theme-dark .form-group input[type="url"]::placeholder,
body.theme-dark .form-group input[type="password"]::placeholder {
  color: #9aa0a6;
  opacity: 0.8;
}

/* 深色主题下的背景设置提示 */
body.theme-dark .bg-setup-prompt {
  background: #3c4043;
  border-color: #5f6368;
}

/* 深色主题下的同步模态框优化 */
body.theme-dark .sync-status {
  background: #3c4043;
  border: 1px solid #5f6368;
}

body.theme-dark .sync-section {
  border-bottom-color: #5f6368;
}

body.theme-dark .sync-message.success {
  background: rgba(52, 168, 83, 0.15);
  color: #81c995;
  border-color: rgba(52, 168, 83, 0.3);
}

body.theme-dark .sync-message.error {
  background: rgba(234, 67, 53, 0.15);
  color: #f28b82;
  border-color: rgba(234, 67, 53, 0.3);
}

body.theme-dark .sync-message.info {
  background: rgba(66, 133, 244, 0.15);
  color: #8ab4f8;
  border-color: rgba(66, 133, 244, 0.3);
}

body.theme-dark .status-value.connected {
  color: #81c995;
}

body.theme-dark .status-value.disconnected {
  color: #f28b82;
}

body.theme-dark .sql-code {
  background: #202124;
  border-color: #5f6368;
  color: #e8eaed;
}

/* 深色主题下的按钮优化 */
body.theme-dark .primary-btn {
  background-color: #8ab4f8;
  color: #202124;
  box-shadow: 0 2px 4px rgba(138, 180, 248, 0.3);
}

body.theme-dark .primary-btn:hover {
  background-color: #aecbfa;
  box-shadow: 0 4px 8px rgba(138, 180, 248, 0.4);
}

body.theme-dark .secondary-btn {
  border-color: #5f6368;
  color: #9aa0a6;
}

body.theme-dark .secondary-btn:hover {
  background-color: #3c4043;
  border-color: #8ab4f8;
  color: #8ab4f8;
}

body.theme-dark .danger-btn {
  border-color: #f28b82;
  color: #f28b82;
}

body.theme-dark .danger-btn:hover {
  background-color: rgba(242, 139, 130, 0.15);
}

/* 深色主题下的表单和文本优化 */
body.theme-dark .form-group label {
  color: #e8eaed;
}

body.theme-dark .form-group small {
  color: #9aa0a6;
}

body.theme-dark .warning-text {
  color: #fdd663;
}

body.theme-dark .info-note {
  background: rgba(138, 180, 248, 0.1);
  border-color: rgba(138, 180, 248, 0.3);
  color: #aecbfa;
}

body.theme-dark .info-note .material-symbols-rounded {
  color: #8ab4f8;
}

/* 深色主题下的同步模态框详细优化 */
body.theme-dark .status-label {
  color: #9aa0a6;
}

body.theme-dark .status-value {
  color: #e8eaed;
}

body.theme-dark .setup-guide {
  color: #e8eaed;
}

body.theme-dark .setup-guide ol {
  color: #e8eaed;
}

body.theme-dark .setup-guide li {
  color: #e8eaed;
}

body.theme-dark .setup-guide p {
  color: #e8eaed;
}

body.theme-dark .setup-guide code {
  background: #3c4043;
  color: #8ab4f8;
}

body.theme-dark .setup-guide a {
  color: #8ab4f8;
}

body.theme-dark .setup-guide a:hover {
  color: #aecbfa;
}

body.theme-dark .link-btn {
  color: #8ab4f8;
}

body.theme-dark .link-btn:hover {
  color: #aecbfa;
}

body.theme-dark .sync-section h3 {
  color: #e8eaed;
}

body.theme-dark .config-form small {
  color: #9aa0a6;
}

/* 深色主题下的特殊文字颜色 */
body.theme-dark small[style*="color: #e74c3c"] {
  color: #f28b82 !important;
}

body.theme-dark span[style*="color: #e74c3c"] {
  color: #f28b82 !important;
}

/* 深色主题下的SQL脚本区域 */
body.theme-dark .sql-script {
  background: #202124;
  border: 1px solid #5f6368;
  border-radius: 8px;
  padding: 16px;
}

body.theme-dark .sql-script pre {
  color: #e8eaed;
  background: transparent;
}

/* 深色主题下的模态框标题 */
body.theme-dark .modal-header h2 {
  color: #e8eaed;
}

/* 深色主题下的关闭按钮 */
body.theme-dark .close-modal {
  color: #9aa0a6;
}

body.theme-dark .close-modal:hover {
  color: #e8eaed;
}

/* 深色主题下的输入框增强对比度 */
body.theme-dark input[type="text"],
body.theme-dark input[type="url"],
body.theme-dark input[type="password"] {
  background-color: #202124 !important;
  border-color: #5f6368 !important;
  color: #ffffff !important;
  font-weight: 500 !important;
}

body.theme-dark input[type="text"]:focus,
body.theme-dark input[type="url"]:focus,
body.theme-dark input[type="password"]:focus {
  background-color: #1a1a1a !important;
  border-color: #8ab4f8 !important;
  color: #ffffff !important;
}

body.theme-dark input[type="text"]:hover,
body.theme-dark input[type="url"]:hover,
body.theme-dark input[type="password"]:hover {
  background-color: #1a1a1a !important;
  border-color: #80868b !important;
}

body.theme-dark input[type="text"]::placeholder,
body.theme-dark input[type="url"]::placeholder,
body.theme-dark input[type="password"]::placeholder {
  color: #9aa0a6 !important;
  opacity: 0.8 !important;
}

/* 背景容器 */
.background-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -2;
  background-color: var(--bg-color);
  transition: background-color 0.3s ease;
}

.background-container.has-bg-image {
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.background-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  background-color: var(--overlay-color);
  transition: background-color 0.3s ease;
}

/* 主题和背景图片设置模态框样式 */

/* 统一的区域样式 */
.modal-section {
  margin-bottom: 32px;
  padding-bottom: 24px;
}

.modal-section:not(:last-child) {
  border-bottom: 1px solid var(--border-color);
}

.modal-section:first-child {
  margin-top: 0;
  padding-top: 0;
}

/* 统一的区域标题样式 */
.modal-section h3,
.themes-section h3,
.bg-image-section h3 {
  margin: 0 0 18px 0;
  color: var(--text-color);
  font-size: 17px;
  font-weight: 500;
  letter-spacing: 0.02em;
  line-height: 1.3;
}

/* 主题选择区域 */
.themes-section {
  margin-bottom: 28px;
  padding-bottom: 20px;
  border-bottom: 1px solid var(--border-color);
}

.theme-options {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  margin: 0;
}

.theme-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.theme-option:hover {
  transform: scale(1.05);
}

.theme-option.active .theme-preview {
  border: 3px solid var(--primary-color);
  box-shadow: 0 4px 12px rgba(66, 133, 244, 0.2);
}

.theme-preview {
  width: 80px;
  height: 80px;
  border-radius: 14px;
  margin-bottom: 10px;
  border: 3px solid transparent;
  box-shadow: 0 2px 6px var(--shadow-color);
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.theme-preview.theme-default {
  background-color: #fff;
}

.theme-preview.theme-blue {
  background-color: #e8f0fe;
}

.theme-preview.theme-green {
  background-color: #e6f4ea;
}

.theme-preview.theme-purple {
  background-color: #f3e8fd;
}

.theme-preview.theme-pink {
  background-color: #fce4ec;
}

.theme-preview.theme-dark {
  background-color: #292a2d;
}



/* 背景图片设置提示界面 */
.bg-setup-prompt {
  text-align: center;
  padding: 40px 20px;
  background: var(--hover-color);
  border-radius: 12px;
  border: 2px dashed var(--border-color);
}

.setup-prompt-content {
  max-width: 300px;
  margin: 0 auto;
}

.setup-prompt-icon {
  margin-bottom: 20px;
}

.setup-prompt-icon .material-symbols-rounded {
  font-size: 48px;
  color: var(--primary-color);
  opacity: 0.8;
}

.bg-setup-prompt h4 {
  margin: 0 0 16px 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--text-color);
}

.bg-setup-prompt p {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: var(--text-secondary);
  line-height: 1.5;
}

.bg-setup-prompt p:last-of-type {
  margin-bottom: 24px;
}

.bg-setup-prompt button {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  font-size: 14px;
  font-weight: 500;
}

.bg-setup-prompt button .material-symbols-rounded {
  font-size: 18px;
}

.bg-setup-prompt code {
  background: rgba(0, 0, 0, 0.1);
  padding: 2px 6px;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
  font-size: 13px;
  color: var(--primary-color);
  font-weight: 600;
}

/* 背景图片预览区域 */
.background-preview {
  width: 100%;
  height: 200px;
  border-radius: 20px;
  border: 3px dashed var(--border-color);
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  background: linear-gradient(135deg, var(--hover-color) 0%, rgba(66, 133, 244, 0.02) 100%);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.04);
}

.background-preview:hover {
  border-color: var(--primary-color);
  background: linear-gradient(135deg, rgba(66, 133, 244, 0.08) 0%, rgba(66, 133, 244, 0.03) 100%);
  transform: translateY(-3px);
  box-shadow: 0 12px 40px rgba(66, 133, 244, 0.15);
  border-style: solid;
}

.background-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: none;
  border-radius: 17px;
  transition: transform 0.3s ease;
}

.background-preview img.has-image {
  display: block;
}

.background-preview:hover img.has-image {
  transform: scale(1.02);
}

/* 无背景图片占位符 */
.no-bg-placeholder {
  color: var(--text-secondary);
  font-size: 16px;
  font-weight: 500;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  text-align: center;
  padding: 20px;
  transition: all 0.3s ease;
}

.no-bg-placeholder::before {
  content: "";
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, var(--primary-color) 0%, #3367d6 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
  opacity: 0.8;
  transition: all 0.3s ease;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M21 19V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2zM8.5 13.5l2.5 3.01L14.5 12l4.5 6H5l3.5-4.5z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
  background-size: 24px;
}

.background-preview:hover .no-bg-placeholder::before {
  transform: scale(1.1);
  opacity: 1;
  box-shadow: 0 4px 15px rgba(66, 133, 244, 0.3);
}

.background-preview:hover .no-bg-placeholder {
  color: var(--primary-color);
  transform: translateY(-2px);
}

.file-input {
  width: 0.1px;
  height: 0.1px;
  opacity: 0;
  overflow: hidden;
  position: absolute;
  z-index: -1;
}

/* 文件上传按钮样式 */
.file-input-label {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  background: linear-gradient(135deg, var(--primary-color) 0%, #3367d6 100%);
  color: white;
  padding: 16px 32px;
  border-radius: 18px;
  cursor: pointer;
  font-size: 16px;
  font-weight: 500;
  line-height: 1.2;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 6px 25px rgba(66, 133, 244, 0.3);
  position: relative;
  overflow: hidden;
  min-width: 200px;
  border: none;
  text-decoration: none;
  letter-spacing: 0.02em;
  backdrop-filter: blur(10px);
  vertical-align: middle;
}

.file-input-label::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.25), transparent);
  transition: left 0.6s ease;
  pointer-events: none;
}

.file-input-label:hover::before {
  left: 100%;
}

.file-input-label:hover {
  background: linear-gradient(135deg, #3367d6 0%, #2c5aa0 100%);
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 12px 40px rgba(66, 133, 244, 0.45);
}

.file-input-label:active {
  transform: translateY(-2px) scale(1.01);
  box-shadow: 0 6px 25px rgba(66, 133, 244, 0.35);
  transition: all 0.15s ease;
}

.file-input-label span {
  font-size: 20px;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.15));
  display: inline-flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
  transition: transform 0.3s ease;
  vertical-align: middle;
}

.file-input-label:hover span {
  transform: scale(1.1);
}

/* 上传区域样式 */
.upload-area {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 18px;
  padding: 28px 24px;
  border: 2px dashed var(--border-color);
  border-radius: 20px;
  background: linear-gradient(135deg, var(--hover-color) 0%, rgba(66, 133, 244, 0.02) 100%);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.upload-area::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(66, 133, 244, 0.05), transparent);
  transition: left 0.6s ease;
  pointer-events: none;
}

.upload-area:hover {
  border-color: var(--primary-color);
  background: linear-gradient(135deg, rgba(66, 133, 244, 0.08) 0%, rgba(66, 133, 244, 0.03) 100%);
  transform: translateY(-3px);
  box-shadow: 0 12px 40px rgba(66, 133, 244, 0.12);
  border-style: solid;
}

.upload-area:hover::before {
  left: 100%;
}

/* 上传提示文本 */
.upload-hint {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px;
  text-align: center;
  margin-top: 4px;
}

.upload-hint small {
  color: var(--text-secondary);
  font-size: 13px;
  line-height: 1.5;
  font-weight: 400;
  transition: color 0.3s ease;
}

.upload-hint small:first-child {
  font-weight: 500;
  color: var(--text-color);
  opacity: 0.8;
}

.upload-area:hover .upload-hint small {
  color: var(--primary-color);
}

.upload-area:hover .upload-hint small:first-child {
  color: var(--primary-color);
  opacity: 1;
}



.form-actions.centered {
  justify-content: center;
  gap: 20px;
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid var(--border-color);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  margin: 0;
  padding: 0;
  background-color: var(--bg-color);
  color: var(--text-color);
  transition: background-color 0.3s ease, color 0.3s ease;
  min-height: 100vh;
  /* 防止字体加载时的闪烁 */
  font-display: swap;
  /* 隐藏滚动条但保持滚动功能 */
  overflow-y: auto;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE/Edge */
}

/* 隐藏 Webkit 浏览器的滚动条 */
body::-webkit-scrollbar {
  display: none;
}

/* 加载状态样式 */
body:not(.loaded) {
  /* 在加载完成前稍微降低透明度 */
  opacity: 0.95;
}

body.loaded {
  opacity: 1;
  transition: opacity 0.3s ease-in-out;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

/* Header and Search */
header {
  margin-bottom: 50px;
  display: flex;
  justify-content: center;
  padding-top: 30px;
}

.search-container {
  display: flex;
  justify-content: center;
  margin-bottom: 50px;
  position: relative;
  overflow: visible;
}

.search-box {
  display: flex;
  align-items: stretch;
  width: 720px;
  max-width: 95%;
  min-height: 56px;
  background-color: var(--card-bg-color);
  border: 1px solid #dfe1e5;
  border-radius: 28px;
  box-shadow: 0 2px 5px 1px rgba(64, 60, 67, 0.16);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  flex-direction: column;
  overflow: visible;
}

.search-box:hover {
  box-shadow: 0 2px 8px 1px rgba(64, 60, 67, 0.24);
  border-color: rgba(223, 225, 229, 0);
}

.search-box:focus-within {
  box-shadow: 0 2px 8px 1px rgba(64, 60, 67, 0.24);
  border-color: rgba(223, 225, 229, 0);
  outline: none;
}

/* 当有搜索结果时，搜索框保持圆角但下拉框紧贴 */
.search-box.has-results {
  border-radius: 28px;
  box-shadow: 0 2px 8px 1px rgba(64, 60, 67, 0.24);
}

/* 当有搜索结果时，搜索框保持完整圆角 */
.search-box.has-results {
  border-radius: 28px;
  box-shadow: 0 2px 8px 1px rgba(64, 60, 67, 0.24);
}

/* 当有搜索结果时，下拉框与搜索框有小间隙但视觉连接 */
.search-box.has-results .search-dropdown {
  margin-top: 4px;
  border-radius: 16px;
  border: 1px solid #dfe1e5;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
}

/* 搜索输入行 */
.search-input-row {
  display: flex;
  align-items: center;
  height: 56px;
  flex-shrink: 0;
}

/* 搜索引擎选择器 */
.search-engine-selector {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  position: relative;
  border-radius: 28px 0 0 28px;
}

.search-engine-selector:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

.search-engine-icon {
  width: 24px;
  height: 24px;
  margin-right: 10px;
  border-radius: 3px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.search-engine-icon img {
  width: 20px;
  height: 20px;
  border-radius: 3px;
  object-fit: cover;
}

.search-engine-dropdown {
  color: #5f6368;
  font-size: 20px;
  display: flex;
  align-items: center;
  transition: transform 0.2s ease;
}

.search-engine-selector.open .search-engine-dropdown {
  transform: rotate(180deg);
}

/* 下拉菜单 */
.search-engine-dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: var(--card-bg-color);
  border: 1px solid #dfe1e5;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 9999;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.2s ease;
  margin-top: 8px;
  min-width: 200px;
  overflow: visible;
}

.search-engine-selector.open .search-engine-dropdown-menu {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.search-engine-option {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  border-radius: 8px;
  margin: 4px;
}

.search-engine-option:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

.search-engine-option.active {
  background-color: rgba(66, 133, 244, 0.1);
  color: var(--primary-color);
}

.search-engine-option img {
  width: 20px;
  height: 20px;
  margin-right: 12px;
  border-radius: 2px;
}

.search-engine-option .material-symbols-rounded {
  width: 20px;
  height: 20px;
  margin-right: 12px;
  font-size: 20px !important;
  color: #9aa0a6 !important;
  display: flex;
  align-items: center;
  justify-content: center;
}

.search-engine-option span:not(.material-symbols-rounded) {
  font-size: 14px;
  font-weight: 500;
}

/* 搜索输入区域 */
.search-input-container {
  flex: 1;
  padding: 0 20px;
}

.search-input-container input {
  width: 100%;
  height: 100%;
  border: none;
  background: transparent;
  font-size: 18px;
  color: var(--text-color);
  outline: none;
  font-family: arial, sans-serif;
}

.search-input-container input::placeholder {
  color: #9aa0a6;
  font-size: 18px;
}

/* 搜索按钮 */
.search-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 56px;
  height: 56px;
  background: none;
  border: none;
  cursor: pointer;
  color: #5f6368;
  transition: all 0.2s ease;
  border-radius: 0 28px 28px 0;
}

.search-button:hover {
  background-color: rgba(0, 0, 0, 0.04);
  color: var(--primary-color);
}

.search-button:active {
  background-color: rgba(0, 0, 0, 0.08);
}

.search-button span {
  font-size: 24px;
}

/* 搜索下拉框 - 现代分离式设计 */
.search-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: var(--card-bg-color);
  border: 1px solid #dfe1e5;
  border-radius: 16px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
  max-height: 400px;
  overflow-y: auto;
  overflow-x: hidden;
  display: none;
  padding: 8px 0;
  z-index: 999;
  margin-top: 4px;
}

.search-result-item {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  margin: 0 8px;
  border-radius: 8px;
  margin-bottom: 4px;
}

.search-result-item:last-child {
  margin-bottom: 8px;
}

.search-result-item:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

.search-result-item.no-results {
  justify-content: center;
  color: var(--text-secondary);
  cursor: default;
  padding: 20px;
}

.search-result-item.no-results:hover {
  background-color: transparent;
}

.search-result-item.no-results span.material-symbols-rounded {
  margin-right: 8px;
  font-size: 20px;
}

/* 搜索下拉框滚动条样式 */
.search-dropdown::-webkit-scrollbar {
  width: 6px;
}

.search-dropdown::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 0 16px 16px 0;
  margin: 8px 0; /* 上下留出空间，避免刺穿圆角 */
}

.search-dropdown::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.15);
  border-radius: 3px;
  border: 2px solid transparent;
  background-clip: content-box;
  margin: 8px 0; /* 确保滚动条滑块不会延伸到圆角区域 */
}

.search-dropdown::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.25);
  background-clip: content-box;
}



.result-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  flex-shrink: 0;
}

.result-icon.category-icon {
  color: white;
}

.result-icon.category-icon span {
  font-size: 20px;
}

.result-icon.shortcut-icon {
  color: white;
  font-size: 18px;
  font-weight: 600;
}

.result-icon.shortcut-icon img {
  width: 24px;
  height: 24px;
  border-radius: 4px;
}

.result-content {
  flex: 1;
  min-width: 0;
}

.result-title {
  font-size: 16px;
  font-weight: 500;
  color: var(--text-color);
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.result-subtitle {
  font-size: 14px;
  color: var(--text-secondary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.result-title mark {
  background: rgba(66, 133, 244, 0.2);
  color: var(--primary-color);
  padding: 1px 3px;
  border-radius: 3px;
  font-weight: 600;
}

/* 深色主题下的搜索框适配 */
.theme-dark .search-box {
  background-color: #303134;
  border-color: #5f6368;
}

.theme-dark .search-box:hover,
.theme-dark .search-box:focus-within {
  background-color: #303134;
  border-color: #5f6368;
  box-shadow: 0 2px 8px 1px rgba(32, 33, 36, 0.28);
}

.theme-dark .search-dropdown {
  background-color: #303134;
  border-color: #5f6368;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.4), 0 4px 8px rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(8px);
}

.theme-dark .search-box.has-results .search-dropdown {
  border: 1px solid #5f6368;
  background-color: #303134;
}

.theme-dark .search-engine-selector:hover {
  background-color: rgba(255, 255, 255, 0.08);
}

.theme-dark .search-engine-dropdown {
  color: #e8eaed;
}

.theme-dark .search-engine-dropdown-menu {
  background: #202124;
  border-color: #5f6368;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.4), 0 4px 8px rgba(0, 0, 0, 0.2);
}

.theme-dark .search-engine-option {
  color: #e8eaed;
}

.theme-dark .search-engine-option:hover {
  background-color: rgba(255, 255, 255, 0.12);
  color: #ffffff;
}

.theme-dark .search-engine-option.active {
  background-color: rgba(138, 180, 248, 0.2);
  color: #8ab4f8;
}

.theme-dark .search-engine-option span:not(.material-symbols-rounded) {
  color: inherit;
  font-weight: 500;
}

.theme-dark .search-engine-option .material-symbols-rounded {
  color: #9aa0a6 !important;
}

.theme-dark .search-input-container input {
  color: #e8eaed;
}

.theme-dark .search-input-container input::placeholder {
  color: #9aa0a6;
}

.theme-dark .search-button {
  color: #9aa0a6;
}

.theme-dark .search-button:hover {
  background-color: rgba(255, 255, 255, 0.08);
  color: #e8eaed;
}



.theme-dark .search-result-item {
  border-radius: 8px;
  transition: background-color 0.2s ease, transform 0.1s ease;
}

.theme-dark .search-result-item:hover {
  background-color: rgba(255, 255, 255, 0.12);
  transform: translateY(-1px);
}

.theme-dark .result-title {
  color: #e8eaed;
  font-weight: 500;
}

.theme-dark .result-subtitle {
  color: #9aa0a6;
  opacity: 0.9;
}

.theme-dark .result-title mark {
  background: rgba(138, 180, 248, 0.25);
  color: #8ab4f8;
  border-radius: 4px;
  padding: 2px 4px;
  font-weight: 600;
}

.theme-dark .search-result-item.no-results {
  color: #9aa0a6;
  background-color: transparent;
}

.theme-dark .search-result-item.no-results:hover {
  background-color: transparent;
  transform: none;
}

/* 暗夜主题下的搜索下拉框滚动条 */
.theme-dark .search-dropdown::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  background-clip: content-box;
}

.theme-dark .search-dropdown::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
  background-clip: content-box;
}



/* 其他主题的搜索框适配 */
.theme-blue .search-box:focus-within {
  box-shadow: 0 2px 8px 1px rgba(25, 103, 210, 0.24);
}

.theme-green .search-box:focus-within {
  box-shadow: 0 2px 8px 1px rgba(52, 168, 83, 0.24);
}

.theme-purple .search-box:focus-within {
  box-shadow: 0 2px 8px 1px rgba(142, 36, 170, 0.24);
}

.theme-pink .search-box:focus-within {
  box-shadow: 0 2px 8px 1px rgba(233, 30, 99, 0.24);
}

/* 浮动按钮 */
.floating-buttons {
  position: fixed;
  right: 24px;
  bottom: 24px;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 12px;
  z-index: 10;
}

.floating-buttons-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: bottom right;
}

.floating-buttons-group.collapsed {
  opacity: 0;
  visibility: hidden;
  transform: scale(0.8) translateY(20px);
  pointer-events: none;
}

.floating-buttons-group.expanded {
  opacity: 1;
  visibility: visible;
  transform: scale(1) translateY(0);
  pointer-events: all;
}

.view-btn, .add-btn, .menu-btn {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: transform 0.2s ease, background-color 0.2s ease, box-shadow 0.2s ease;
  box-shadow: 0 2px 8px var(--shadow-color);
}

/* 次要按钮样式 */
.secondary-btn {
  background-color: var(--card-bg-color);
  color: var(--text-secondary);
}

.secondary-btn.active, .secondary-btn:hover {
  background-color: var(--primary-color);
  color: white;
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 主要按钮样式 */
.primary-btn {
  background-color: var(--primary-color);
  color: white;
  position: relative;
  overflow: hidden;
}

.primary-btn:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* 菜单按钮的展开/收起指示 */
.menu-btn .material-symbols-rounded {
  transition: transform 0.3s ease;
}

.floating-buttons-group.expanded + .menu-btn .material-symbols-rounded {
  transform: rotate(90deg);
}

/* 菜单按钮特殊样式 */
.menu-btn {
  background-color: var(--primary-color);
  color: white;
  position: relative;
  overflow: hidden;
}

.menu-btn:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* Categories Container */
#categories-container {
  display: grid;
  gap: 8px;
  transition: all var(--transition-speed);
  margin-top: 20px;
}

#categories-container.grid-view {
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
}

#categories-container.list-view {
  grid-template-columns: 1fr;
}

/* Category Card */
.category-card {
  background-color: var(--card-bg-color);
  border-radius: 12px;
  box-shadow: 0 2px 8px var(--shadow-color);
  overflow: hidden;
  transition: box-shadow 0.25s cubic-bezier(0.4, 0, 0.2, 1), transform 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: transform, box-shadow;
}

.category-card:hover {
  box-shadow: 0 6px 16px var(--shadow-color);
  transform: translate3d(0, -3px, 0);
}

.category-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px 20px;
  cursor: pointer;
  user-select: none;
  border-bottom: 1px solid var(--border-color);
  transition: background-color 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.category-header:hover {
  background-color: rgba(0, 0, 0, 0.03);
}

.category-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 500;
  color: var(--text-color);
}

.category-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  margin-right: 10px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.category-actions {
  display: flex;
  gap: 8px;
}

.category-actions button {
  width: 32px;
  height: 32px;
  background: none;
  border: none;
  cursor: pointer;
  border-radius: 50%;
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s cubic-bezier(0.4, 0, 0.2, 1),
              color 0.2s cubic-bezier(0.4, 0, 0.2, 1),
              transform 0.15s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: background-color, color, transform;
}

.category-actions button:hover {
  background-color: var(--hover-color);
  color: var(--primary-color);
  transform: scale(1.1);
}

.category-content {
  padding: 0 20px 0;
  max-height: 0;
  overflow: hidden;
  opacity: 0;
  transition: max-height 0.15s ease-out,
              opacity 0.1s ease-out,
              padding 0.1s ease-out;
}

.category-content.expanded {
  max-height: 800px;
  padding: 10px 20px 20px;
  opacity: 1;
}

/* Modal */
.modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity var(--transition-speed);
}

.modal.show {
  opacity: 1;
}

.modal-content {
  background-color: var(--card-bg-color);
  border-radius: var(--border-radius);
  box-shadow: 0 5px 20px var(--shadow-color);
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  transform: translateY(20px);
  transition: transform var(--transition-speed);
  overflow: hidden; /* 确保子元素不会超出圆角边界 */
}

.modal.show .modal-content {
  transform: translateY(0);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid var(--border-color);
  border-radius: var(--border-radius) var(--border-radius) 0 0;
  flex-shrink: 0;
}

.modal-header h2 {
  margin: 0;
  font-size: 20px;
  font-weight: 500;
  color: var(--text-color);
}

.close-modal {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: var(--text-secondary);
}

.modal-body {
  padding: 20px;
  flex: 1;
  overflow-y: auto;
  border-radius: 0 0 var(--border-radius) var(--border-radius);
  /* 确保滚动条不会破坏圆角 */
  overflow-x: hidden;
  position: relative;
}

/* 自定义滚动条样式 */
.modal-body::-webkit-scrollbar {
  width: 6px;
}

.modal-body::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 0 var(--border-radius) var(--border-radius) 0;
  margin: 12px 0; /* 上下留出空间，避免刺穿圆角 */
}

.modal-body::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.15);
  border-radius: 3px;
  border: 2px solid transparent;
  background-clip: content-box;
  margin: 12px 0; /* 确保滚动条滑块不会延伸到圆角区域 */
}

.modal-body::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.25);
  background-clip: content-box;
}

/* 暗夜主题下的滚动条 */
.theme-dark .modal-body::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  background-clip: content-box;
}

.theme-dark .modal-body::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
  background-clip: content-box;
}





/* Form Elements */
.form-group {
  margin-bottom: 24px;
}

.form-group label {
  display: block;
  margin-bottom: 12px;
  font-size: 15px;
  font-weight: 500;
  color: var(--text-color);
  letter-spacing: 0.02em;
  line-height: 1.3;
}

/* 背景图片预览区域不需要标签，所以移除这个特殊调整 */

/* 背景图片设置区域整体样式 */
.bg-image-section {
  margin-bottom: 28px;
  padding-bottom: 20px;
  /* 移除 border-top，因为已经在统一样式中处理 */
}

/* 背景图片控制区域 - 重新设计统一布局 */
.bg-image-controls {
  display: flex;
  flex-direction: column;
}

.bg-image-controls .form-group {
  margin-bottom: 0;
}

/* 为每个功能区域设置统一的间距 */
.bg-image-controls .form-group:not(:last-child) {
  margin-bottom: 30px;
}

/* 确保按钮区域有足够的上边距 */
.bg-image-controls .form-actions {
  margin-top: 32px;
}

/* 统一的功能区域标签样式 */
.bg-image-controls .form-group label,
.modal-section .form-group label {
  margin: 0 0 16px 0;
  font-size: 17px;
  font-weight: 500;
  color: var(--text-color);
  display: block;
  letter-spacing: 0.02em;
  line-height: 1.3;
}

/* 透明度滑块区域优化 */
.range-with-value {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px 20px;
  background: var(--hover-color);
  border-radius: 16px;
  border: 1px solid var(--border-color);
  transition: all 0.3s ease;
}

.range-with-value:hover {
  border-color: var(--primary-color);
  background: rgba(66, 133, 244, 0.05);
  box-shadow: 0 4px 15px rgba(66, 133, 244, 0.1);
}

.range-with-value input[type="range"] {
  flex: 1;
  -webkit-appearance: none;
  height: 6px;
  background: var(--border-color);
  border-radius: 6px;
  outline: none;
  transition: background 0.3s ease;
}

.range-with-value input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: var(--primary-color);
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(66, 133, 244, 0.3);
  transition: all 0.3s ease;
}

.range-with-value input[type="range"]::-webkit-slider-thumb:hover {
  transform: scale(1.2);
  box-shadow: 0 4px 12px rgba(66, 133, 244, 0.4);
}

.range-with-value span {
  font-size: 14px;
  font-weight: 600;
  color: var(--primary-color);
  min-width: 45px;
  text-align: center;
  background: rgba(66, 133, 244, 0.1);
  padding: 6px 12px;
  border-radius: 8px;
}

/* 确保模态框内容的整体间距 */
.modal-body > *:first-child {
  margin-top: 0;
}

.modal-body > *:last-child {
  margin-bottom: 0;
}



.form-group input[type="text"],
.form-group input[type="url"],
.form-group input[type="password"] {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid var(--border-color);
  border-radius: 8px;
  font-size: 14px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  box-sizing: border-box;
  background-color: var(--card-bg-color);
  color: var(--text-color);
  transition: all 0.2s ease;
  outline: none;
}

.form-group input[type="text"]:focus,
.form-group input[type="url"]:focus,
.form-group input[type="password"]:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(66, 133, 244, 0.1);
  transform: translateY(-1px);
}

.form-group input[type="text"]:hover,
.form-group input[type="url"]:hover,
.form-group input[type="password"]:hover {
  border-color: #c0c0c0;
}

.form-group input[type="text"]::placeholder,
.form-group input[type="url"]::placeholder,
.form-group input[type="password"]::placeholder {
  color: #999;
  opacity: 1;
}

.form-group input[type="color"] {
  width: 100%;
  height: 40px;
  border: 1px solid var(--border-color);
  border-radius: 5px;
  cursor: pointer;
}

.form-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
}

.save-btn {
  padding: 12px 24px;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(66, 133, 244, 0.2);
}

.save-btn:hover {
  background-color: #3367d6;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(66, 133, 244, 0.3);
}

.save-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.delete-btn {
  padding: 12px 24px;
  background-color: transparent;
  color: var(--danger-color);
  border: 2px solid var(--danger-color);
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.delete-btn:hover {
  background-color: rgba(234, 67, 53, 0.1);
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(234, 67, 53, 0.2);
}

/* Empty State */
.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  padding: 60px 20px;
}

.empty-state-content {
  text-align: center;
  max-width: 480px;
  animation: fadeInUp 0.6s ease-out;
}

.empty-state-icon {
  margin-bottom: 24px;
}

.empty-state-icon .material-symbols-rounded {
  font-size: 72px;
  color: var(--primary-color);
  opacity: 0.8;
  animation: pulse 2s infinite;
}

.empty-state-title {
  font-size: 28px;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 16px;
  line-height: 1.3;
}

.empty-state-description {
  font-size: 16px;
  color: var(--text-secondary);
  margin-bottom: 32px;
  line-height: 1.6;
}

.empty-state-actions {
  margin-bottom: 40px;
  position: relative;
}

.empty-state-actions::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 120%;
  height: 120%;
  border-radius: 20px;
  background: linear-gradient(135deg, rgba(66, 133, 244, 0.1) 0%, rgba(52, 168, 83, 0.1) 100%);
  animation: buttonGlow 3s ease-in-out infinite;
  z-index: -1;
}

.empty-state-actions .primary-btn {
  font-size: 16px;
  font-weight: 600;
  padding: 16px 32px;
  border-radius: 16px;
  background: linear-gradient(135deg, #4285f4 0%, #34a853 100%);
  border: none;
  color: white;
  box-shadow: 0 8px 24px rgba(66, 133, 244, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  cursor: pointer;
}

.empty-state-actions .primary-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.empty-state-actions .primary-btn:hover {
  transform: translateY(-3px) scale(1.02);
  box-shadow: 0 12px 32px rgba(66, 133, 244, 0.4);
  background: linear-gradient(135deg, #3367d6 0%, #2d8f47 100%);
}

.empty-state-actions .primary-btn:hover::before {
  left: 100%;
}

.empty-state-actions .primary-btn:active {
  transform: translateY(-1px) scale(0.98);
  box-shadow: 0 4px 16px rgba(66, 133, 244, 0.3);
}

.empty-state-actions .primary-btn .material-symbols-rounded {
  margin-right: 10px;
  font-size: 22px;
  vertical-align: middle;
  transition: transform 0.3s ease;
}

.empty-state-actions .primary-btn:hover .material-symbols-rounded {
  transform: rotate(90deg) scale(1.1);
}

.empty-state-tips {
  border-top: 1px solid var(--border-color);
  padding-top: 24px;
}

.tip-item {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-size: 14px;
  color: var(--text-secondary);
  opacity: 0.8;
}

.tip-item .material-symbols-rounded {
  font-size: 18px;
  color: #ffa726;
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes buttonGlow {
  0%, 100% {
    opacity: 0.3;
    transform: translate(-50%, -50%) scale(1);
  }
  50% {
    opacity: 0.6;
    transform: translate(-50%, -50%) scale(1.1);
  }
}

/* URL Input Container */
.url-input-container {
  display: flex;
  align-items: center;
  gap: 5px;
}

.url-input-container input {
  flex: 1;
}

.url-input-container button {
  background: none;
  border: none;
  cursor: pointer;
  color: var(--primary-color);
  padding: 5px;
  border-radius: 5px;
  transition: background-color var(--transition-speed);
}

.url-input-container button:hover {
  background-color: var(--hover-color);
}

/* Icon Options */
.icon-options {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-top: 8px;
}

.icon-option {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border: 2px solid var(--border-color);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  background-color: var(--card-bg-color);
  min-width: 120px;
}

.icon-option:hover {
  border-color: var(--primary-color);
  background-color: var(--hover-color);
}

.icon-option input[type="radio"] {
  margin-right: 8px;
  width: 16px;
  height: 16px;
  cursor: pointer;
  accent-color: var(--primary-color);
  /* 确保单选按钮在所有浏览器中都可见 */
  -webkit-appearance: radio;
  -moz-appearance: radio;
  appearance: radio;
  /* 防止被其他样式覆盖 */
  opacity: 1 !important;
  position: relative !important;
  z-index: 1 !important;
  /* 确保尺寸 */
  min-width: 16px !important;
  min-height: 16px !important;
  flex-shrink: 0;
}

/* 选中状态样式 - 使用多种选择器确保兼容性 */
.icon-option input[type="radio"]:checked + label {
  color: var(--primary-color);
  font-weight: 500;
}

.icon-option:has(input[type="radio"]:checked) {
  border-color: var(--primary-color);
  background-color: rgba(66, 133, 244, 0.1);
  color: var(--primary-color);
  font-weight: 500;
}

/* 备用样式 - 为不支持:has()的浏览器 */
.icon-option.selected {
  border-color: var(--primary-color);
  background-color: rgba(66, 133, 244, 0.1);
  color: var(--primary-color);
  font-weight: 500;
}

.icon-option label {
  cursor: pointer;
  font-size: 14px;
  color: var(--text-color);
  margin: 0;
  user-select: none;
}

/* Favicon Preview */
.favicon-container {
  display: flex;
  align-items: center;
  gap: 10px;
}

.favicon-container img {
  width: 32px;
  height: 32px;
  object-fit: contain;
  border-radius: 4px;
  background-color: var(--hover-color);
}

.note {
  font-size: 12px;
  color: var(--text-secondary);
  margin: 0;
}

/* Hidden Elements */
.hidden {
  display: none;
}

/* Empty shortcuts state */
.empty-shortcuts {
  grid-column: 1 / -1;
  text-align: center;
  color: #888;
  padding: 10px;
  font-size: 14px;
}

/* 空分类提示样式 */
.empty-shortcuts-message {
  grid-column: 1 / -1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30px 20px;
  color: var(--text-secondary);
  text-align: center;
}

.empty-shortcuts-message .material-symbols-rounded {
  font-size: 36px;
  margin-bottom: 10px;
  opacity: 0.7;
}

.empty-shortcuts-message p {
  margin: 5px 0;
  font-size: 14px;
}

.empty-shortcuts-message .add-shortcut-btn {
  margin-top: 15px;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 20px;
  padding: 8px 16px;
  font-size: 14px;
  cursor: pointer;
  display: flex;
  align-items: center;
  transition: background-color 0.2s ease, transform 0.2s ease, box-shadow 0.2s ease;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.empty-shortcuts-message .add-shortcut-btn:hover {
  background-color: #3367d6;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.empty-shortcuts-message .add-shortcut-btn .material-symbols-rounded {
  font-size: 18px;
  margin-right: 5px;
  margin-bottom: 0;
  opacity: 1;
}

/* Responsive */
@media (max-width: 768px) {
  .container {
    padding: 15px;
  }
  
  .search-box {
    width: 100%;
  }
  
  #categories-container.grid-view {
    grid-template-columns: 1fr;
  }
  
  .floating-buttons {
    right: 16px;
    bottom: 16px;
  }
  
  .view-btn, .add-btn {
    width: 40px;
    height: 40px;
  }
  
  .shortcuts-grid {
    grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
  }
  
  .shortcut-icon {
    width: 40px;
    height: 40px;
  }
}

/* Animation for loading spinner */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 拖拽相关样式 */
.drag-handle {
  opacity: 0.6;
  margin-right: 8px;
  cursor: grab;
  transition: opacity 0.2s ease;
  font-size: 16px !important;
}

.drag-handle:hover {
  opacity: 1;
}

.drag-handle:active {
  cursor: grabbing;
}

.category-header:hover .drag-handle {
  opacity: 0.8;
}

.draggable-shortcut {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.draggable-shortcut:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 拖拽基础样式 - 自然和谐版 */
.dragging {
  opacity: 0.5 !important;
  transform: rotate(5deg);
  z-index: 1000;
  pointer-events: none;
  transition: opacity 0.2s ease-out, transform 0.2s ease-out;
}

/* 拖拽元素的自然过渡 */
.category-card, .shortcut {
  transition: opacity 0.3s ease, transform 0.3s ease,
              border 0.2s ease, background-color 0.2s ease,
              box-shadow 0.2s ease;
}

.drag-placeholder {
  border: 2px dashed var(--primary-color);
  border-radius: var(--border-radius);
  background-color: rgba(66, 133, 244, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  min-height: 60px;
}

.placeholder-content {
  color: var(--primary-color);
  font-size: 14px;
  font-weight: 500;
  opacity: 0.8;
}

.drag-placeholder:hover {
  background-color: rgba(66, 133, 244, 0.15);
  border-color: var(--primary-color);
}

/* 拖拽时的分类卡片样式 */
.category-card.dragging {
  transform: rotate(3deg) scale(0.95);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

/* 拖拽时的快捷方式样式 */
.shortcut-item.dragging {
  transform: rotate(5deg) scale(0.9);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

/* Animation for highlighting selected category */
@keyframes highlight-pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(66, 133, 244, 0.4);
    transform: scale(1);
  }
  50% {
    box-shadow: 0 0 0 10px rgba(66, 133, 244, 0);
    transform: scale(1.02);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(66, 133, 244, 0);
    transform: scale(1);
  }
}

/* 快捷方式网格 */
.shortcuts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  gap: 15px;
}

.shortcuts-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  justify-content: flex-start;
  padding: 0 16px;
}

.shortcut {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px;
  border-radius: 10px;
  cursor: pointer;
  transition: background-color 0.2s cubic-bezier(0.4, 0, 0.2, 1),
              transform 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  text-decoration: none;
  color: var(--text-color);
  will-change: transform, background-color;
}

.shortcut:hover {
  background-color: var(--hover-color);
  transform: translate3d(0, -2px, 0);
}

.list-view .shortcuts-list {
  justify-content: flex-start;
  padding: 0 calc((100% - 12 * 80px - 11 * 8px) / 2);
}

.list-view .shortcut {
  flex-direction: column;
  align-items: center;
  padding: 10px 4px;
  width: 80px;
  flex: 0 0 80px;
}

.shortcut-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
  color: white;
  font-weight: 500;
  font-size: 20px;
  flex-shrink: 0;
  overflow: hidden;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  background-color: #4285f4; /* 默认背景色 */
  text-align: center;
  line-height: 1;
}

.list-view .shortcut-icon {
  width: 48px;
  height: 48px;
  margin-bottom: 8px;
  margin-right: 0;
  font-size: 20px;
  border-radius: 12px;
  background-color: #4285f4; /* 默认背景色 */
  text-align: center;
  line-height: 1;
}

.shortcut-img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  border-radius: inherit;
  display: block;
}

.list-view .shortcut-img {
  width: 100%;
  height: 100%;
}

.shortcut-name {
  font-size: 14px;
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
  color: var(--text-color);
}

.list-view .shortcut-name {
  text-align: center;
  font-size: 12px;
}

/* 已移除不再需要的add-shortcut相关样式 */

/* 同步相关样式 */
.sync-status {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.status-item:last-child {
  margin-bottom: 0;
}

.status-label {
  font-weight: 500;
  color: #666;
}

.status-value {
  font-weight: 600;
}

.status-value.connected {
  color: #28a745;
}

.status-value.disconnected {
  color: #dc3545;
}

.sync-section {
  margin-bottom: 24px;
  padding-bottom: 24px;
  border-bottom: 1px solid #e9ecef;
}

.sync-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.config-form .form-group {
  margin-bottom: 16px;
}

.config-form small {
  display: block;
  margin-top: 6px;
  color: #6c757d;
  font-size: 12px;
  line-height: 1.4;
  font-style: italic;
}

.setup-guide ol {
  padding-left: 20px;
  line-height: 1.6;
}

.setup-guide li {
  margin-bottom: 8px;
}

.setup-guide code {
  background: var(--hover-color);
  padding: 2px 6px;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
  font-size: 13px;
  color: var(--primary-color);
  font-weight: 600;
}

.link-btn {
  background: none;
  border: none;
  color: #007bff;
  text-decoration: underline;
  cursor: pointer;
  padding: 0;
  font-size: inherit;
}

.link-btn:hover {
  color: #0056b3;
}

.sql-script {
  position: relative;
  margin-top: 16px;
}

.sql-script pre {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 16px;
  font-size: 12px;
  line-height: 1.4;
  overflow-x: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.sync-message {
  padding: 12px 16px;
  border-radius: 4px;
  margin-bottom: 16px;
  font-weight: 500;
}

.sync-message.success {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.sync-message.error {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.sync-message.info {
  background: #d1ecf1;
  color: #0c5460;
  border: 1px solid #bee5eb;
}

/* 通用按钮样式 */
.primary-btn {
  padding: 12px 24px;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(66, 133, 244, 0.2);
  display: inline-flex;
  align-items: center;
  gap: 8px;
  justify-content: center;
}

.primary-btn:hover {
  background-color: #3367d6;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(66, 133, 244, 0.3);
}

.primary-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.secondary-btn {
  padding: 10px 20px;
  background-color: transparent;
  color: var(--text-secondary);
  border: 2px solid var(--border-color);
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  justify-content: center;
}

.secondary-btn:hover {
  background-color: var(--hover-color);
  color: var(--text-color);
  border-color: var(--primary-color);
  transform: translateY(-1px);
}

.secondary-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.danger-btn {
  padding: 10px 20px;
  background-color: transparent;
  color: var(--danger-color);
  border: 2px solid var(--danger-color);
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  justify-content: center;
}

.danger-btn:hover {
  background-color: rgba(234, 67, 53, 0.1);
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(234, 67, 53, 0.2);
}

.danger-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* 响应式设计 - 同步界面 */
@media (max-width: 768px) {
  .sync-status {
    padding: 12px;
  }

  .status-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .form-actions {
    flex-direction: column;
    gap: 10px;
  }

  .form-actions button {
    width: 100%;
  }
}

/* 配置切换区域样式 */
.config-switch-section {
  margin-bottom: 28px;
  padding-bottom: 20px;
  border-bottom: 1px solid var(--border-color);
}

/* 配置提示样式 */
.config-prompt {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  background: linear-gradient(135deg, rgba(66, 133, 244, 0.05) 0%, rgba(66, 133, 244, 0.02) 100%);
  border: 2px dashed rgba(66, 133, 244, 0.3);
  border-radius: 12px;
  gap: 20px;
}

.prompt-content {
  display: flex;
  align-items: center;
  gap: 16px;
  flex: 1;
}

.prompt-content .material-symbols-rounded {
  font-size: 32px;
  color: var(--primary-color);
  opacity: 0.8;
}

.prompt-text p {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 500;
  color: var(--text-color);
}

.prompt-text small {
  font-size: 14px;
  color: var(--text-secondary);
}

/* 配置选择器样式 */
.config-selector {
  background: var(--card-bg-color);
  border: 2px solid var(--border-color);
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 25px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

/* 新的配置显示布局 */
.current-config-display {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20px;
}

.config-info {
  flex: 1;
}

.config-label {
  font-size: 14px;
  color: var(--text-secondary);
  margin-bottom: 8px;
  display: block;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.config-name-display {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

#current-config-name {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-color);
  margin: 0;
}

.config-user-id {
  font-size: 13px;
  color: var(--primary-color);
  background: rgba(66, 133, 244, 0.1);
  padding: 4px 10px;
  border-radius: 20px;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-weight: 500;
  border: 1px solid rgba(66, 133, 244, 0.2);
}

.config-management-buttons {
  display: flex;
  gap: 12px;
  align-items: center;
  flex-shrink: 0;
}

.config-management-buttons button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 18px;
  border-radius: 10px;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  border: none;
  white-space: nowrap;
  position: relative;
  overflow: hidden;
}

.config-management-buttons button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
  transform: translateX(-100%);
  transition: transform 0.6s;
}

.config-management-buttons button:hover::before {
  transform: translateX(100%);
}

.config-management-buttons .primary-btn {
  background: linear-gradient(135deg, var(--primary-color) 0%, #3367d6 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(66, 133, 244, 0.3);
}

.config-management-buttons .primary-btn:hover {
  background: linear-gradient(135deg, #3367d6 0%, #2851a3 100%);
  box-shadow: 0 6px 20px rgba(66, 133, 244, 0.4);
  transform: translateY(-2px);
}

.config-management-buttons .primary-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(66, 133, 244, 0.3);
}

.config-management-buttons .secondary-btn {
  background: var(--bg-color);
  color: var(--text-color);
  border: 2px solid var(--border-color);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.config-management-buttons .secondary-btn:hover {
  background: var(--hover-color);
  border-color: var(--primary-color);
  color: var(--primary-color);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.config-management-buttons .secondary-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.config-management-buttons .material-symbols-rounded {
  font-size: 18px;
  transition: transform 0.2s ease;
}

.config-management-buttons button:hover .material-symbols-rounded {
  transform: scale(1.1);
}

.current-config {
  display: flex;
  align-items: center;
  gap: 12px;
}

.config-label {
  font-size: 15px;
  font-weight: 500;
  color: var(--text-color);
  white-space: nowrap;
}

.config-dropdown {
  position: relative;
  flex: 1;
}

.config-dropdown-btn {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 12px 16px;
  background: var(--card-bg-color);
  border: 2px solid var(--border-color);
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
}

.config-dropdown-btn:hover {
  border-color: var(--primary-color);
  background: rgba(66, 133, 244, 0.05);
}

.config-dropdown-btn .config-name {
  font-weight: 600;
  color: var(--text-color);
}

.config-dropdown-btn .config-user {
  color: var(--text-secondary);
  margin-left: 8px;
}

.config-dropdown-btn .material-symbols-rounded {
  font-size: 20px;
  color: var(--text-secondary);
  transition: transform 0.3s ease;
}

.config-dropdown.open .config-dropdown-btn .material-symbols-rounded {
  transform: rotate(180deg);
}

.config-dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: var(--card-bg-color);
  border: 2px solid var(--border-color);
  border-radius: 10px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  margin-top: 4px;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.3s ease;
}

.config-dropdown.open .config-dropdown-menu {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.config-dropdown-menu .config-list {
  padding: 8px 0;
  border-bottom: 1px solid var(--border-color);
}

.config-dropdown-menu .config-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  cursor: pointer;
  transition: background 0.3s ease;
}

.config-dropdown-menu .config-item:hover {
  background: var(--hover-color);
}

.config-dropdown-menu .config-item.active {
  background: rgba(66, 133, 244, 0.1);
  color: var(--primary-color);
}

.config-dropdown-menu .config-item.active::before {
  content: '✓';
  margin-right: 8px;
  font-weight: bold;
}

.config-actions {
  padding: 8px 0;
}

.config-action-item {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
  padding: 12px 16px;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 14px;
  color: var(--text-color);
  transition: background 0.3s ease;
}

.config-action-item:hover {
  background: var(--hover-color);
}

.config-action-item .material-symbols-rounded {
  font-size: 18px;
}

/* 新建配置模态框样式 */
#new-config-modal .modal-content {
  max-width: 450px;
}

.info-note {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: rgba(66, 133, 244, 0.05);
  border-radius: 8px;
  font-size: 14px;
  color: var(--text-secondary);
  margin-bottom: 20px;
}

.info-note .material-symbols-rounded {
  font-size: 18px;
  color: var(--primary-color);
}

/* 配置管理样式 */
.config-list {
  margin-bottom: 20px;
}

.config-item-placeholder {
  text-align: center;
  padding: 40px 20px;
  color: var(--text-secondary);
  background: var(--hover-color);
  border-radius: 12px;
  border: 2px dashed var(--border-color);
}

.config-item-placeholder p {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 500;
}

.config-item-placeholder small {
  font-size: 14px;
  opacity: 0.8;
}

.config-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  margin-bottom: 12px;
  background: var(--card-bg-color);
  border: 2px solid var(--border-color);
  border-radius: 12px;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
}

.config-item:hover {
  border-color: var(--primary-color);
  background: rgba(66, 133, 244, 0.05);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(66, 133, 244, 0.1);
}

.config-item.active {
  border-color: var(--primary-color);
  background: rgba(66, 133, 244, 0.08);
  box-shadow: 0 4px 15px rgba(66, 133, 244, 0.15);
}

.config-item.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: var(--primary-color);
  border-radius: 0 4px 4px 0;
}

.config-info {
  flex: 1;
  min-width: 0;
}

.config-name {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 4px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.config-name .active-badge {
  background: var(--primary-color);
  color: white;
  font-size: 12px;
  font-weight: 500;
  padding: 2px 8px;
  border-radius: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.config-details {
  font-size: 14px;
  color: var(--text-secondary);
  line-height: 1.4;
}

.config-meta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
  margin-left: 16px;
}

.config-sync-time {
  font-size: 12px;
  color: var(--text-secondary);
  white-space: nowrap;
}

.config-shortcut-count {
  font-size: 12px;
  background: var(--hover-color);
  color: var(--text-color);
  padding: 2px 8px;
  border-radius: 8px;
  white-space: nowrap;
}

.config-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.config-actions button {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  font-size: 14px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.config-item-actions {
  display: flex;
  gap: 8px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.config-item:hover .config-item-actions {
  opacity: 1;
}

.config-action-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 8px;
  background: var(--hover-color);
  color: var(--text-secondary);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  font-size: 18px;
}

.config-action-btn:hover {
  background: var(--primary-color);
  color: white;
  transform: scale(1.1);
}

.config-action-btn.delete:hover {
  background: var(--danger-color);
}

/* 配置编辑模态框样式 */
#config-edit-modal .modal-content {
  max-width: 500px;
}

#config-edit-form .form-group {
  margin-bottom: 20px;
}

#config-edit-form .form-group:last-of-type {
  margin-bottom: 0;
}

/* 配置管理模态框样式 */
.config-management-content {
  max-width: 900px;
  width: 90vw;
  max-height: 85vh;
}

/* 分页控件样式 */
.config-pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20px;
  padding: 16px 0;
  border-top: 1px solid var(--border-color);
}

.pagination-info {
  font-size: 14px;
  color: var(--text-secondary);
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.pagination-btn {
  min-width: 36px;
  height: 36px;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  background: var(--background-color);
  color: var(--text-color);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  transition: all 0.2s ease;
  padding: 0 12px;
}

.pagination-btn:hover:not(.disabled) {
  background: var(--hover-color);
  border-color: var(--primary-color);
}

.pagination-btn.active {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.pagination-btn.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: var(--hover-color);
}

.pagination-ellipsis {
  padding: 0 8px;
  color: var(--text-secondary);
  font-size: 14px;
}

/* 配置操作栏样式优化 */
.config-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.config-actions .sort-select {
  min-width: 120px;
}

#page-size-select {
  min-width: 100px;
  font-size: 14px;
}

.config-search-bar {
  display: flex;
  gap: 16px;
  margin-bottom: 24px;
  align-items: center;
  flex-wrap: wrap;
  background: var(--hover-color);
  padding: 20px;
  border-radius: 12px;
  border: 1px solid var(--border-color);
}

.search-input-wrapper {
  position: relative;
  flex: 1;
  min-width: 280px;
}

.search-input-wrapper input {
  width: 100%;
  padding: 12px 45px 12px 16px;
  border: 2px solid var(--border-color);
  border-radius: 10px;
  font-size: 14px;
  transition: all 0.3s ease;
  background: var(--bg-color);
  color: var(--text-color);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.search-input-wrapper input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(66, 133, 244, 0.1);
  transform: translateY(-1px);
}

.search-icon {
  position: absolute;
  right: 14px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-secondary);
  pointer-events: none;
  font-size: 18px;
}

.config-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

#new-config-btn-main {
  background: linear-gradient(135deg, var(--secondary-color) 0%, #0d8043 100%);
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 10px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(15, 157, 88, 0.3);
  display: flex;
  align-items: center;
  gap: 8px;
  white-space: nowrap;
}

#new-config-btn-main:hover {
  background: linear-gradient(135deg, #0d8043 0%, #0a5d32 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(15, 157, 88, 0.4);
}

#new-config-btn-main:active {
  transform: translateY(0);
}

.sort-select {
  padding: 10px 14px;
  border: 2px solid var(--border-color);
  border-radius: 8px;
  background: var(--bg-color);
  color: var(--text-color);
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 140px;
}

.sort-select:focus {
  outline: none;
  border-color: var(--primary-color);
}

.config-list-container {
  max-height: 450px;
  overflow-y: auto;
  padding-right: 4px;
}

.config-list {
  display: grid;
  gap: 8px;
}

.config-card {
  border: 2px solid var(--border-color);
  border-radius: 10px;
  padding: 14px 16px;
  background: var(--card-bg-color);
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  gap: 16px;
}

.config-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: transparent;
  transition: background 0.3s ease;
}

.config-card:hover {
  border-color: var(--primary-color);
  box-shadow: 0 6px 20px rgba(66, 133, 244, 0.15);
  transform: translateY(-2px);
}

.config-card:hover::before {
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

.config-card.active {
  border-color: var(--primary-color);
  background: rgba(66, 133, 244, 0.05);
  box-shadow: 0 4px 16px rgba(66, 133, 244, 0.2);
}

.config-card.active::before {
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

.config-card-header {
  display: block;
  margin-bottom: 0;
}

.config-info {
  flex: 1;
  min-width: 0;
}

.config-name {
  font-size: 15px;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 4px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.config-badge {
  background: var(--primary-color);
  color: white;
  font-size: 9px;
  padding: 2px 6px;
  border-radius: 10px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.config-user {
  font-size: 12px;
  color: var(--text-secondary);
  margin-bottom: 6px;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  background: rgba(66, 133, 244, 0.08);
  padding: 2px 6px;
  border-radius: 4px;
  display: inline-block;
}

.config-meta {
  display: flex;
  gap: 8px;
  font-size: 11px;
  color: var(--text-secondary);
  margin-bottom: 0;
  flex-wrap: wrap;
}

.config-meta-item {
  display: flex;
  align-items: center;
  gap: 3px;
  background: var(--hover-color);
  padding: 2px 6px;
  border-radius: 4px;
}

.config-actions-row {
  display: flex;
  gap: 4px;
  flex-shrink: 0;
  align-items: center;
}

.config-btn {
  padding: 4px 8px;
  border: 1px solid var(--border-color);
  border-radius: 5px;
  background: var(--bg-color);
  color: var(--text-color);
  font-size: 11px;
  cursor: pointer;
  transition: all 0.2s;
  white-space: nowrap;
  font-weight: 500;
}

.config-btn:hover {
  background: var(--hover-color);
  transform: translateY(-1px);
}

.config-btn.primary {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.config-btn.primary:hover {
  background: #3367d6;
  box-shadow: 0 2px 8px rgba(66, 133, 244, 0.3);
}

.config-btn.danger {
  color: var(--danger-color);
  border-color: var(--danger-color);
}

.config-btn.danger:hover {
  background: var(--danger-color);
  color: white;
  box-shadow: 0 2px 8px rgba(234, 67, 53, 0.3);
}

.config-empty {
  text-align: center;
  padding: 40px 20px;
  color: var(--text-secondary);
  background: var(--hover-color);
  border-radius: 12px;
  border: 2px dashed var(--border-color);
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.6;
}

.config-empty h3 {
  margin: 0 0 8px 0;
  color: var(--text-color);
  font-size: 18px;
}

.config-empty p {
  margin: 0 0 20px 0;
  color: var(--text-secondary);
  font-size: 14px;
}

/* 配置选择器响应式设计 */
@media (max-width: 768px) {
  .current-config-display {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }

  .config-management-buttons {
    justify-content: center;
    flex-wrap: wrap;
  }

  .config-management-buttons button {
    flex: 1;
    min-width: 140px;
  }

  .config-name-display {
    justify-content: center;
    text-align: center;
  }

  .config-selector {
    padding: 20px;
  }
}

/* 配置同步按钮样式优化 */
#setup-sync-btn,
#setup-supabase-btn {
  background: linear-gradient(135deg, #4285f4 0%, #34a853 100%);
  color: white;
  border: none;
  padding: 14px 28px;
  border-radius: 12px;
  font-size: 15px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 16px rgba(66, 133, 244, 0.3);
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  gap: 8px;
  justify-content: center;
  text-transform: none;
  letter-spacing: 0.5px;
  min-width: 200px;
}

#setup-sync-btn::before,
#setup-supabase-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

#setup-sync-btn:hover::before,
#setup-supabase-btn:hover::before {
  left: 100%;
}

#setup-sync-btn:hover,
#setup-supabase-btn:hover {
  background: linear-gradient(135deg, #3367d6 0%, #2d7d32 100%);
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(66, 133, 244, 0.4);
}

#setup-sync-btn:active,
#setup-supabase-btn:active {
  transform: translateY(-1px);
  box-shadow: 0 4px 16px rgba(66, 133, 244, 0.3);
}

#setup-sync-btn .material-symbols-rounded,
#setup-supabase-btn .material-symbols-rounded {
  font-size: 20px;
  transition: transform 0.2s ease;
}

#setup-sync-btn:hover .material-symbols-rounded,
#setup-supabase-btn:hover .material-symbols-rounded {
  transform: rotate(360deg);
}

/* 背景图片功能禁用警告样式 */
.bg-warning {
  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
  border: 2px solid #ffc107;
  border-radius: 10px;
  padding: 16px;
  margin-bottom: 20px;
  box-shadow: 0 4px 12px rgba(255, 193, 7, 0.2);
}

.warning-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.warning-icon {
  font-size: 24px;
  flex-shrink: 0;
}

.warning-text {
  flex: 1;
}

.warning-text strong {
  color: #856404;
  font-size: 14px;
  font-weight: 600;
  display: block;
  margin-bottom: 4px;
}

.warning-text p {
  color: #856404;
  font-size: 13px;
  margin: 0;
  opacity: 0.8;
}

.warning-btn {
  background: linear-gradient(135deg, #ffc107 0%, #ffb300 100%);
  color: #856404;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.warning-btn:hover {
  background: linear-gradient(135deg, #ffb300 0%, #ff8f00 100%);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(255, 193, 7, 0.3);
}

/* ========================================
   拖拽动画增强样式
   ======================================== */

/* 拖拽目标高亮 */
.drag-target {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.drag-target.category-target {
  border-top: 4px solid #ff4444 !important;
  background: linear-gradient(to bottom,
    rgba(255, 68, 68, 0.15),
    rgba(255, 68, 68, 0.05)
  ) !important;
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(255, 68, 68, 0.3);
}

.drag-target.shortcut-target {
  border-left: 4px solid #4285f4 !important;
  background: linear-gradient(to right,
    rgba(66, 133, 244, 0.15),
    rgba(66, 133, 244, 0.05)
  ) !important;
  transform: translateX(-2px);
  box-shadow: 0 4px 20px rgba(66, 133, 244, 0.3);
}

/* 拖拽动画关键帧 */
@keyframes placeholderPulse {
  0%, 100% {
    opacity: 0.6;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.02);
  }
}

@keyframes gradientShift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

@keyframes dragEnter {
  0% {
    transform: scale(1);
    opacity: 0;
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes dragLeave {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  100% {
    transform: scale(0.95);
    opacity: 0;
  }
}

/* 拖拽开始动画 */
.drag-starting {
  animation: dragStart 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes dragStart {
  0% {
    transform: scale(1) rotate(0deg);
  }
  50% {
    transform: scale(1.05) rotate(1deg);
  }
  100% {
    transform: scale(0.95) rotate(3deg);
  }
}

/* 拖拽结束动画 */
.drag-ending {
  animation: dragEnd 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes dragEnd {
  0% {
    transform: scale(0.95) rotate(3deg);
    opacity: 0.6;
  }
  50% {
    transform: scale(1.02) rotate(0deg);
    opacity: 0.8;
  }
  100% {
    transform: scale(1) rotate(0deg);
    opacity: 1;
  }
}

/* 拖拽预览增强 */
.drag-preview-enhanced {
  position: fixed;
  pointer-events: none;
  z-index: 9999;
  opacity: 0.85;
  transform: rotate(5deg) scale(0.9);
  border-radius: 12px;
  box-shadow: 0 12px 48px rgba(0, 0, 0, 0.3);
  background: linear-gradient(135deg, rgba(255,255,255,0.95), rgba(255,255,255,0.8));
  backdrop-filter: blur(15px);
  border: 2px solid rgba(66, 133, 244, 0.6);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 占位符增强样式 */
.drag-placeholder-enhanced {
  border: 3px dashed #4285f4;
  border-radius: 12px;
  background: linear-gradient(45deg,
    rgba(66, 133, 244, 0.1),
    rgba(66, 133, 244, 0.2),
    rgba(66, 133, 244, 0.1)
  );
  background-size: 200% 200%;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 80px;
  margin: 10px 0;
  color: #4285f4;
  font-size: 14px;
  font-weight: 600;
  animation: placeholderPulse 2s ease-in-out infinite, gradientShift 3s ease-in-out infinite;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.drag-placeholder-enhanced.category {
  min-height: 100px;
  border-color: #ff4444;
  background: linear-gradient(45deg,
    rgba(255, 68, 68, 0.1),
    rgba(255, 68, 68, 0.2),
    rgba(255, 68, 68, 0.1)
  );
  color: #ff4444;
  font-size: 16px;
}

.drag-placeholder-enhanced.shortcut {
  min-height: 80px;
  width: 120px;
  margin: 5px;
  border-color: #4285f4;
}

/* ========================================
   自然和谐的拖拽动画优化
   ======================================== */

/* 拖拽目标的自然高亮效果 */
.drag-target-natural {
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.drag-target-natural.category {
  border-top: 3px solid #ff4444;
  background: linear-gradient(to bottom,
    rgba(255, 68, 68, 0.08) 0%,
    rgba(255, 68, 68, 0.04) 50%,
    rgba(255, 68, 68, 0.02) 100%
  );
  transform: translateY(-1px);
  box-shadow: 0 2px 12px rgba(255, 68, 68, 0.15);
}

.drag-target-natural.shortcut {
  border-left: 3px solid #4285f4;
  background: linear-gradient(to right,
    rgba(66, 133, 244, 0.08) 0%,
    rgba(66, 133, 244, 0.04) 50%,
    rgba(66, 133, 244, 0.02) 100%
  );
  transform: translateX(-1px);
  box-shadow: 0 2px 12px rgba(66, 133, 244, 0.15);
}

/* 拖拽时的自然反馈 */
.dragging-natural {
  opacity: 0.75 !important;
  transform: rotate(1.5deg) scale(0.98);
  z-index: 1000;
  pointer-events: none;
  transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  filter: brightness(1.05);
}

/* 拖拽结束的自然回弹 */
.drag-ending-natural {
  animation: naturalBounceBack 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

@keyframes naturalBounceBack {
  0% {
    opacity: 0.75;
    transform: rotate(1.5deg) scale(0.98);
  }
  60% {
    opacity: 0.9;
    transform: rotate(0deg) scale(1.01);
  }
  100% {
    opacity: 1;
    transform: rotate(0deg) scale(1);
  }
}

/* 自然的脉动效果 */
@keyframes naturalPulse {
  0%, 100% {
    opacity: 0.8;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.005);
  }
}

/* 拖拽开始的自然动画 */
.drag-starting-natural {
  animation: naturalDragStart 0.25s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

@keyframes naturalDragStart {
  0% {
    transform: scale(1) rotate(0deg);
    opacity: 1;
  }
  100% {
    transform: scale(0.98) rotate(1.5deg);
    opacity: 0.75;
  }
}

/* 悬停时的自然预览 */
.category-header:hover {
  transition: all 0.2s ease;
}

.shortcut:hover {
  transition: all 0.2s ease;
}

/* 拖拽区域的自然指示 */
.drag-handle-natural {
  opacity: 0.4;
  transition: opacity 0.2s ease;
}

.category-header:hover .drag-handle-natural {
  opacity: 0.7;
}

/* 自然的边框动画 */
.border-animate-in {
  animation: borderFadeIn 0.2s ease-out;
}

.border-animate-out {
  animation: borderFadeOut 0.15s ease-in;
}

@keyframes borderFadeIn {
  0% {
    border-color: transparent;
    background: transparent;
    transform: translateY(0) translateX(0);
    box-shadow: none;
  }
  100% {
    border-color: currentColor;
    background: currentColor;
    transform: translateY(-1px) translateX(-1px);
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
  }
}

@keyframes borderFadeOut {
  0% {
    border-color: currentColor;
    background: currentColor;
    transform: translateY(-1px) translateX(-1px);
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
  }
  100% {
    border-color: transparent;
    background: transparent;
    transform: translateY(0) translateX(0);
    box-shadow: none;
  }
}

/* 配置列表加载状态 */
.config-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: var(--text-color);
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--border-color);
  border-top: 3px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

.loading-text {
  font-size: 14px;
  opacity: 0.7;
}

/* 配置列表错误状态 */
.config-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: var(--text-color);
}

.error-icon {
  font-size: 32px;
  margin-bottom: 16px;
}

.error-text {
  font-size: 14px;
  margin-bottom: 16px;
  opacity: 0.7;
}

.retry-btn {
  padding: 8px 16px;
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: background-color 0.2s;
}

.retry-btn:hover {
  background: #3367d6;
}

/* 全局通知系统样式 */
.notification {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 10000;
  max-width: 400px;
  min-width: 300px;
  background: var(--card-bg-color);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  border: 1px solid var(--border-color);
  opacity: 0;
  transform: translateX(100%);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.notification-show {
  opacity: 1;
  transform: translateX(0);
}

.notification-hide {
  opacity: 0;
  transform: translateX(100%);
}

.notification-content {
  display: flex;
  align-items: center;
  padding: 16px;
  gap: 12px;
}

.notification-icon {
  font-size: 24px;
  flex-shrink: 0;
}

.notification-message {
  flex: 1;
  font-size: 14px;
  line-height: 1.4;
  color: var(--text-color);
}

.notification-close {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 6px;
  color: var(--text-secondary);
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.notification-close:hover {
  background: rgba(0, 0, 0, 0.1);
  color: var(--text-color);
}

.notification-close .material-symbols-rounded {
  font-size: 18px;
}

/* 不同类型的通知颜色 */
.notification-success .notification-icon {
  color: #10b981;
}

.notification-error .notification-icon {
  color: #ef4444;
}

.notification-warning .notification-icon {
  color: #f59e0b;
}

.notification-info .notification-icon {
  color: #3b82f6;
}

/* 深色主题下的通知样式 */
.theme-dark .notification {
  background: #2d3748;
  border-color: #4a5568;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
}

.theme-dark .notification-close:hover {
  background: rgba(255, 255, 255, 0.1);
}

/* 多个通知的堆叠效果 */
.notification:nth-child(n+2) {
  top: calc(20px + (80px * var(--notification-index, 0)));
}

/* 移动端响应式 */
@media (max-width: 768px) {
  .notification {
    top: 10px;
    right: 10px;
    left: 10px;
    max-width: none;
    min-width: auto;
  }

  .notification:nth-child(n+2) {
    top: calc(10px + (70px * var(--notification-index, 0)));
  }
}

