/* Material Symbols Rounded - 本地字体 */
/*
 * 本地化的 Material Symbols 字体
 * 替代在线 Google Fonts，提升加载性能
 */

/* 本地字体定义 */
@font-face {
  font-display: swap; /* 优化字体加载性能 */
  font-family: 'Material Symbols Rounded';
  font-style: normal;
  font-weight: 100 700; /* 支持多种字重 */
  src: url('material-symbols-rounded-v255-latin-regular.woff2') format('woff2');
}

/* Material Symbols 基础样式 */
.material-symbols-rounded {
  font-family: 'Material Symbols Rounded', sans-serif;
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-feature-settings: 'liga';
  -webkit-font-smoothing: antialiased;
  font-feature-settings: 'liga';
  font-variation-settings: 'FILL' 0, 'wght' 400, 'GRAD' 0, 'opsz' 24;
}

/* 基础图标样式 */
.icon {
  display: inline-block;
  width: 24px;
  height: 24px;
  line-height: 1;
  text-align: center;
  vertical-align: middle;
  user-select: none;
}

/* SVG图标样式 */
.icon-svg {
  width: 24px;
  height: 24px;
}

.icon-svg svg {
  width: 100%;
  height: 100%;
  display: block;
}

/* Unicode图标样式 */
.icon-unicode {
  font-family: Arial, sans-serif;
  font-size: 18px;
  line-height: 24px;
}

/* 备选图标样式 */
.icon-fallback {
  font-family: Arial, sans-serif;
  font-size: 16px;
  line-height: 24px;
}

/* 仅在字体加载失败时应用的备选样式 */
.material-symbols-fallback {
  font-family: Arial, sans-serif !important;
  font-size: 18px;
  line-height: 24px;
}

/* 改进的备选图标映射 */
.material-symbols-fallback[data-icon="search"]:before,
.material-symbols-fallback:contains("search"):before { content: "🔍"; }

.material-symbols-fallback[data-icon="expand_more"]:before,
.material-symbols-fallback:contains("expand_more"):before { content: "▼"; }

.material-symbols-fallback[data-icon="sync"]:before,
.material-symbols-fallback:contains("sync"):before { content: "⟲"; }

.material-symbols-fallback[data-icon="palette"]:before,
.material-symbols-fallback:contains("palette"):before { content: "🎨"; }

.material-symbols-fallback[data-icon="grid_view"]:before,
.material-symbols-fallback:contains("grid_view"):before { content: "⊞"; }

.material-symbols-fallback[data-icon="view_list"]:before,
.material-symbols-fallback:contains("view_list"):before { content: "☰"; }

.material-symbols-fallback[data-icon="add"]:before,
.material-symbols-fallback:contains("add"):before { content: "+"; font-weight: bold; }

.material-symbols-fallback[data-icon="download"]:before,
.material-symbols-fallback:contains("download"):before { content: "⬇"; }

.material-symbols-fallback[data-icon="cloud_upload"]:before,
.material-symbols-fallback:contains("cloud_upload"):before { content: "⬆"; }

.material-symbols-fallback[data-icon="settings"]:before,
.material-symbols-fallback:contains("settings"):before { content: "⚙"; }

.material-symbols-fallback[data-icon="info"]:before,
.material-symbols-fallback:contains("info"):before { content: "ℹ"; }

/* 常用图标的CSS实现 - 无需字体文件 */
.icon-search:before { content: "🔍"; }
.icon-palette:before { content: "🎨"; }
.icon-grid:before { content: "⊞"; }
.icon-list:before { content: "☰"; }
.icon-add:before { content: "+"; font-weight: bold; }
.icon-download:before { content: "⬇"; }
.icon-upload:before { content: "⬆"; }
.icon-expand-more:before { content: "▼"; }
.icon-expand-less:before { content: "▲"; }
.icon-edit:before { content: "✏"; }
.icon-info:before { content: "ℹ"; }
.icon-sync:before { content: "⟲"; }

/* 更好的图标实现 - 使用CSS绘制 */
.icon-search-css {
  position: relative;
  display: inline-block;
  width: 24px;
  height: 24px;
}

.icon-search-css:before {
  content: "";
  position: absolute;
  top: 4px;
  left: 4px;
  width: 12px;
  height: 12px;
  border: 2px solid currentColor;
  border-radius: 50%;
}

.icon-search-css:after {
  content: "";
  position: absolute;
  top: 14px;
  left: 14px;
  width: 6px;
  height: 2px;
  background: currentColor;
  transform: rotate(45deg);
}
